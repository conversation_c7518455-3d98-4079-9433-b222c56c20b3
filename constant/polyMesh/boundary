/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

9
(
    inletCH4
    {
        type            patch;
        nFaces          5;
        startFace       10159;
    }
    wallOutside
    {
        type            wall;
        inGroups        List<word> 1(wall);
        nFaces          70;
        startFace       10164;
    }
    wallTube
    {
        type            wall;
        inGroups        List<word> 1(wall);
        nFaces          61;
        startFace       10234;
    }
    inletPilot
    {
        type            patch;
        nFaces          5;
        startFace       10295;
    }
    inletAir
    {
        type            patch;
        nFaces          60;
        startFace       10300;
    }
    outlet
    {
        type            patch;
        nFaces          71;
        startFace       10360;
    }
    axis
    {
        type            empty;
        inGroups        List<word> 1(empty);
        nFaces          0;
        startFace       10431;
    }
    frontAndBack_pos
    {
        type            wedge;
        inGroups        List<word> 1(wedge);
        nFaces          5170;
        startFace       10431;
    }
    frontAndBack_neg
    {
        type            wedge;
        inGroups        List<word> 1(wedge);
        nFaces          5170;
        startFace       15601;
    }
)

// ************************************************************************* //
