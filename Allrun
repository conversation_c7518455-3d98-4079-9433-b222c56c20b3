#!/bin/sh
cd ${0%/*} || exit 1    # Run from this directory

# Source tutorial run functions
. $WM_PROJECT_DIR/bin/tools/RunFunctions

# nProcs=4  # Change this number based on your CPU cores

runApplication chemkinToFoam \
               chemkin/grimech30.dat chemkin/thermo30.dat chemkin/transportProperties \
               constant/reactionsGRI constant/thermo.compressibleGasGRI

runApplication blockMesh
runApplication setFields

# runApplication decomposePar

if ! isTest $@
then
    # Run the application without chemistry until 1500 to let the flow develop
    runApplication -a foamDictionary -entry "startTime" -set "2000" system/controlDict
    runApplication -a foamDictionary -entry "writeInterval" -set "500" system/controlDict
    runApplication -a foamDictionary -entry "endTime" -set "3000" system/controlDict
    runApplication -a foamDictionary -entry "chemistry" -set "off" constant/chemistryProperties

    runApplication foamRun
    # runParallel foamRun $nProcs

    # # Run with chemistry until flame reach its full size
    # runApplication -a foamDictionary -entry "startTime" -set "1500" system/controlDict
    # runApplication -a foamDictionary -entry "writeInterval" -set "100" system/controlDict
    # runApplication -a foamDictionary -entry "endTime" -set "5000" system/controlDict
    # runApplication -a foamDictionary -entry "chemistry" -set "on" constant/chemistryProperties
fi

# runParallel foamRun $nProcs


#------------------------------------------------------------------------------
