/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      ascii;
    class       volScalarField;
    location    "0";
    object      H;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [];

internalField   uniform 0;

boundaryField
{
    wallTube
    {
        type            zeroGradient;
    }

    outlet
    {
        type            inletOutlet;
        inletValue      $internalField;
    }

    inletPilot
    {
        type            fixedValue;
        value           uniform 2.48e-5;
    }

    inletAir
    {
        type            fixedValue;
        value           uniform 0;
    }

    wallOutside
    {
        type            zeroGradient;
    }

    inletCH4
    {
        type            fixedValue;
        value           uniform 0;
    }

    frontAndBack_pos
    {
        type            wedge;
    }

    frontAndBack_neg
    {
        type            wedge;
    }
}


// ************************************************************************* //
