/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      ascii;
    class       volScalarField;
    location    "0";
    object      alphat;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [1 -1 -1 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    wallTube
    {
        type            compressible::alphatWallFunction;
        Prt             0.85;
        value           $internalField;
    }

    outlet
    {
        type            calculated;
        value           $internalField;
    }

    inletPilot
    {
        type            calculated;
        value           $internalField;
    }

    inletAir
    {
        type            calculated;
        value           $internalField;
    }

    wallOutside
    {
        type            compressible::alphatWallFunction;
        Prt             0.85;
        value           $internalField;
    }

    inletCH4
    {
        type            calculated;
        value           $internalField;
    }

    frontAndBack_pos
    {
        type            wedge;
    }

    frontAndBack_neg
    {
        type            wedge;
    }
}


// ************************************************************************* //
