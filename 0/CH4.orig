/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      ascii;
    class       volScalarField;
    location    "0";
    object      CH4;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [];

internalField   uniform 0;

boundaryField
{
    inletCH4
    {
        type            fixedValue;
        value           uniform 0.1561;
    }

    wallOutside
    {
        type            zeroGradient;
    }

    wallTube
    {
        type            zeroGradient;
    }

    inletPilot
    {
        type            fixedValue;
        value           uniform 0;
    }

    inletAir
    {
        type            fixedValue;
        value           uniform 0;
    }

    outlet
    {
        type            inletOutlet;
        inletValue      $internalField;
    }

    frontAndBack_pos
    {
        type            wedge;
    }

    frontAndBack_neg
    {
        type            wedge;
    }
}


// ************************************************************************* //
