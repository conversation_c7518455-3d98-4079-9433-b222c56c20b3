/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      ascii;
    class       volScalarField;
    location    "0";
    object      T;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 0 0 1 0 0 0];

internalField   uniform 300;

boundaryField
{
    inletCH4
    {
        type            fixedValue;
        value           uniform 294;
    }

    wallOutside
    {
        type            zeroGradient;
    }

    wallTube
    {
        type            zeroGradient;
    }

    inletPilot
    {
        type            fixedValue;
        value           uniform 1880;
    }

    inletAir
    {
        type            fixedValue;
        value           uniform 291;
    }

    outlet
    {
        type            inletOutlet;
        inletValue      $internalField;
    }

    frontAndBack_pos
    {
        type            wedge;
    }

    frontAndBack_neg
    {
        type            wedge;
    }
}


// ************************************************************************* //
