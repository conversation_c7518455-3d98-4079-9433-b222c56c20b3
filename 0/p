/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      ascii;
    class       volScalarField;
    location    "0";
    object      p;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [1 -1 -2 0 0 0 0];

internalField   uniform 100000;

boundaryField
{
    wallTube
    {
        type            zeroGradient;
    }

    outlet
    {
        type            entrainmentPressure;
        p0              $internalField;
    }

    inletPilot
    {
        type            zeroGradient;
    }

    inletAir
    {
        type            zeroGradient;
    }

    wallOutside
    {
        type            zeroGradient;
    }

    inletCH4
    {
        type            zeroGradient;
    }

    frontAndBack_pos
    {
        type            wedge;
    }

    frontAndBack_neg
    {
        type            wedge;
    }
}


// ************************************************************************* //
