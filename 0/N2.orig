/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      ascii;
    class       volScalarField;
    location    "0";
    object      N2;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [];

internalField   uniform 0.77;

boundaryField
{
    inletCH4
    {
        type            fixedValue;
        value           uniform 0.6473;
    }

    wallOutside
    {
        type            zeroGradient;
    }

    wallTube
    {
        type            zeroGradient;
    }

    inletPilot
    {
        type            fixedValue;
        value           uniform 0.7342;
    }

    inletAir
    {
        type            fixedValue;
        value           uniform 0.77;
    }

    outlet
    {
        type            inletOutlet;
        inletValue      $internalField;
    }

    frontAndBack_pos
    {
        type            wedge;
    }

    frontAndBack_neg
    {
        type            wedge;
    }
}


// ************************************************************************* //
