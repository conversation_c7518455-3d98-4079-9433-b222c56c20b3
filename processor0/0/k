/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       volScalarField;
    location    "0";
    object      k;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -2 0 0 0 0];

internalField   uniform 30;

boundaryField
{
    inletCH4
    {
        type            turbulentIntensityKineticEnergyInlet;
        intensity       0.0458;
        value           nonuniform List<scalar> 
0
;
    }
    wallOutside
    {
        type            kqRWallFunction;
        value           uniform 30;
    }
    wallTube
    {
        type            kqRWallFunction;
        value           nonuniform List<scalar> 
0
;
    }
    inletPilot
    {
        type            turbulentIntensityKineticEnergyInlet;
        intensity       0.0628;
        value           nonuniform List<scalar> 
0
;
    }
    inletAir
    {
        type            turbulentIntensityKineticEnergyInlet;
        intensity       0.0471;
        value           uniform 1;
    }
    outlet
    {
        type            inletOutlet;
        inletValue      nonuniform List<scalar> 
0
;
        value           nonuniform List<scalar> 
0
;
    }
    axis
    {
        type            empty;
    }
    frontAndBack_pos
    {
        type            wedge;
    }
    frontAndBack_neg
    {
        type            wedge;
    }
    procBoundary0to1
    {
        type            processor;
        value           uniform 30;
    }
    procBoundary0to2
    {
        type            processor;
        value           uniform 30;
    }
    procBoundary0to3
    {
        type            processor;
        value           uniform 30;
    }
}


// ************************************************************************* //
