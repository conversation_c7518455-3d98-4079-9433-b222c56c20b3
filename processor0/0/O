/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       volScalarField;
    location    "0";
    object      O;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [];

internalField   uniform 0;

boundaryField
{
    inletCH4
    {
        type            fixedValue;
        value           nonuniform List<scalar> 
0
;
    }
    wallOutside
    {
        type            zeroGradient;
    }
    wallTube
    {
        type            zeroGradient;
    }
    inletPilot
    {
        type            fixedValue;
        value           nonuniform List<scalar> 
0
;
    }
    inletAir
    {
        type            fixedValue;
        value           uniform 0;
    }
    outlet
    {
        type            inletOutlet;
        inletValue      nonuniform List<scalar> 
0
;
        value           nonuniform List<scalar> 
0
;
    }
    axis
    {
        type            empty;
    }
    frontAndBack_pos
    {
        type            wedge;
    }
    frontAndBack_neg
    {
        type            wedge;
    }
    procBoundary0to1
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary0to2
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary0to3
    {
        type            processor;
        value           uniform 0;
    }
}


// ************************************************************************* //
