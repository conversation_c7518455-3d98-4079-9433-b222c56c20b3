/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       volScalarField;
    location    "0";
    object      p;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [1 -1 -2 0 0 0 0];

internalField   uniform 100000;

boundaryField
{
    inletCH4
    {
        type            zeroGradient;
    }
    wallOutside
    {
        type            zeroGradient;
    }
    wallTube
    {
        type            zeroGradient;
    }
    inletPilot
    {
        type            zeroGradient;
    }
    inletAir
    {
        type            zeroGradient;
    }
    outlet
    {
        type            entrainmentPressure;
        rho             rho;
        psi             none;
        gamma           1;
        p0              nonuniform List<scalar> 
0
;
        value           nonuniform List<scalar> 
0
;
    }
    axis
    {
        type            empty;
    }
    frontAndBack_pos
    {
        type            wedge;
    }
    frontAndBack_neg
    {
        type            wedge;
    }
    procBoundary0to1
    {
        type            processor;
        value           uniform 100000;
    }
    procBoundary0to2
    {
        type            processor;
        value           uniform 100000;
    }
    procBoundary0to3
    {
        type            processor;
        value           uniform 100000;
    }
}


// ************************************************************************* //
