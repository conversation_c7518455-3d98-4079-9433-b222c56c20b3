/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

12
(
    inletCH4
    {
        type            patch;
        nFaces          0;
        startFace       2527;
    }
    wallOutside
    {
        type            wall;
        inGroups        List<word> 1(wall);
        nFaces          33;
        startFace       2527;
    }
    wallTube
    {
        type            wall;
        inGroups        List<word> 1(wall);
        nFaces          0;
        startFace       2560;
    }
    inletPilot
    {
        type            patch;
        nFaces          0;
        startFace       2560;
    }
    inletAir
    {
        type            patch;
        nFaces          37;
        startFace       2560;
    }
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       2597;
    }
    axis
    {
        type            empty;
        inGroups        List<word> 1(empty);
        nFaces          0;
        startFace       2597;
    }
    frontAndBack_pos
    {
        type            wedge;
        inGroups        List<word> 1(wedge);
        nFaces          1300;
        startFace       2597;
    }
    frontAndBack_neg
    {
        type            wedge;
        inGroups        List<word> 1(wedge);
        nFaces          1300;
        startFace       3897;
    }
    procBoundary0to1
    {
        type            processor;
        inGroups        List<word> 1(processor);
        nFaces          40;
        startFace       5197;
        matchTolerance  0.0001;
        myProcNo        0;
        neighbProcNo    1;
    }
    procBoundary0to2
    {
        type            processor;
        inGroups        List<word> 1(processor);
        nFaces          32;
        startFace       5237;
        matchTolerance  0.0001;
        myProcNo        0;
        neighbProcNo    2;
    }
    procBoundary0to3
    {
        type            processor;
        inGroups        List<word> 1(processor);
        nFaces          4;
        startFace       5269;
        matchTolerance  0.0001;
        myProcNo        0;
        neighbProcNo    3;
    }
)

// ************************************************************************* //
