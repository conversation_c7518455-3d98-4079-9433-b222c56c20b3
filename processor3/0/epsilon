/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       volScalarField;
    location    "0";
    object      epsilon;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -3 0 0 0 0];

internalField   uniform 30000;

boundaryField
{
    inletCH4
    {
        type            turbulentMixingLengthDissipationRateInlet;
        mixingLength    0.000504;
        phi             phi;
        k               k;
        value           nonuniform List<scalar> 
0
;
    }
    wallOutside
    {
        type            epsilonWallFunction;
        Cmu             0.09;
        kappa           0.41;
        E               9.8;
        value           nonuniform List<scalar> 
0
;
    }
    wallTube
    {
        type            epsilonWallFunction;
        Cmu             0.09;
        kappa           0.41;
        E               9.8;
        value           nonuniform List<scalar> 
0
;
    }
    inletPilot
    {
        type            turbulentMixingLengthDissipationRateInlet;
        mixingLength    0.000735;
        phi             phi;
        k               k;
        value           nonuniform List<scalar> 
0
;
    }
    inletAir
    {
        type            turbulentMixingLengthDissipationRateInlet;
        mixingLength    0.019677;
        value           nonuniform List<scalar> 
0
;
    }
    outlet
    {
        type            inletOutlet;
        inletValue      uniform 30000;
        value           uniform 30000;
    }
    axis
    {
        type            empty;
    }
    frontAndBack_pos
    {
        type            wedge;
    }
    frontAndBack_neg
    {
        type            wedge;
    }
    procBoundary3to0
    {
        type            processor;
        value           uniform 30000;
    }
    procBoundary3to1
    {
        type            processor;
        value           uniform 30000;
    }
    procBoundary3to2
    {
        type            processor;
        value           uniform 30000;
    }
}


// ************************************************************************* //
