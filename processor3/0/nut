/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       volScalarField;
    location    "0";
    object      nut;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -1 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    inletCH4
    {
        type            calculated;
        value           nonuniform List<scalar> 
0
;
    }
    wallOutside
    {
        type            nutkWallFunction;
        Cmu             0.09;
        kappa           0.41;
        E               9.8;
        value           nonuniform List<scalar> 
0
;
    }
    wallTube
    {
        type            nutkWallFunction;
        Cmu             0.09;
        kappa           0.41;
        E               9.8;
        value           nonuniform List<scalar> 
0
;
    }
    inletPilot
    {
        type            calculated;
        value           nonuniform List<scalar> 
0
;
    }
    inletAir
    {
        type            calculated;
        value           nonuniform List<scalar> 
0
;
    }
    outlet
    {
        type            calculated;
        value           uniform 0;
    }
    axis
    {
        type            empty;
    }
    frontAndBack_pos
    {
        type            wedge;
    }
    frontAndBack_neg
    {
        type            wedge;
    }
    procBoundary3to0
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary3to1
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary3to2
    {
        type            processor;
        value           uniform 0;
    }
}


// ************************************************************************* //
