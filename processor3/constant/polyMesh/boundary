/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

12
(
    inletCH4
    {
        type            patch;
        nFaces          0;
        startFace       2490;
    }
    wallOutside
    {
        type            wall;
        inGroups        List<word> 1(wall);
        nFaces          0;
        startFace       2490;
    }
    wallTube
    {
        type            wall;
        inGroups        List<word> 1(wall);
        nFaces          0;
        startFace       2490;
    }
    inletPilot
    {
        type            patch;
        nFaces          0;
        startFace       2490;
    }
    inletAir
    {
        type            patch;
        nFaces          0;
        startFace       2490;
    }
    outlet
    {
        type            patch;
        nFaces          33;
        startFace       2490;
    }
    axis
    {
        type            empty;
        inGroups        List<word> 1(empty);
        nFaces          0;
        startFace       2523;
    }
    frontAndBack_pos
    {
        type            wedge;
        inGroups        List<word> 1(wedge);
        nFaces          1281;
        startFace       2523;
    }
    frontAndBack_neg
    {
        type            wedge;
        inGroups        List<word> 1(wedge);
        nFaces          1281;
        startFace       3804;
    }
    procBoundary3to0
    {
        type            processor;
        inGroups        List<word> 1(processor);
        nFaces          4;
        startFace       5085;
        matchTolerance  0.0001;
        myProcNo        3;
        neighbProcNo    0;
    }
    procBoundary3to1
    {
        type            processor;
        inGroups        List<word> 1(processor);
        nFaces          35;
        startFace       5089;
        matchTolerance  0.0001;
        myProcNo        3;
        neighbProcNo    1;
    }
    procBoundary3to2
    {
        type            processor;
        inGroups        List<word> 1(processor);
        nFaces          34;
        startFace       5124;
        matchTolerance  0.0001;
        myProcNo        3;
        neighbProcNo    2;
    }
)

// ************************************************************************* //
