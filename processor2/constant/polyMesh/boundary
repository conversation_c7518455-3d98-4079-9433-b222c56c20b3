/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

11
(
    inletCH4
    {
        type            patch;
        nFaces          5;
        startFace       2470;
    }
    wallOutside
    {
        type            wall;
        inGroups        List<word> 1(wall);
        nFaces          0;
        startFace       2475;
    }
    wallTube
    {
        type            wall;
        inGroups        List<word> 1(wall);
        nFaces          61;
        startFace       2475;
    }
    inletPilot
    {
        type            patch;
        nFaces          5;
        startFace       2536;
    }
    inletAir
    {
        type            patch;
        nFaces          23;
        startFace       2541;
    }
    outlet
    {
        type            patch;
        nFaces          0;
        startFace       2564;
    }
    axis
    {
        type            empty;
        inGroups        List<word> 1(empty);
        nFaces          0;
        startFace       2564;
    }
    frontAndBack_pos
    {
        type            wedge;
        inGroups        List<word> 1(wedge);
        nFaces          1288;
        startFace       2564;
    }
    frontAndBack_neg
    {
        type            wedge;
        inGroups        List<word> 1(wedge);
        nFaces          1288;
        startFace       3852;
    }
    procBoundary2to0
    {
        type            processor;
        inGroups        List<word> 1(processor);
        nFaces          32;
        startFace       5140;
        matchTolerance  0.0001;
        myProcNo        2;
        neighbProcNo    0;
    }
    procBoundary2to3
    {
        type            processor;
        inGroups        List<word> 1(processor);
        nFaces          34;
        startFace       5172;
        matchTolerance  0.0001;
        myProcNo        2;
        neighbProcNo    3;
    }
)

// ************************************************************************* //
