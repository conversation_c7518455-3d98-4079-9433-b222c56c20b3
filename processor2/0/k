/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       volScalarField;
    location    "0";
    object      k;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -2 0 0 0 0];

internalField   uniform 30;

boundaryField
{
    inletCH4
    {
        type            turbulentIntensityKineticEnergyInlet;
        intensity       0.0458;
        value           uniform 1;
    }
    wallOutside
    {
        type            kqRWallFunction;
        value           nonuniform List<scalar> 
0
;
    }
    wallTube
    {
        type            kqRWallFunction;
        value           uniform 30;
    }
    inletPilot
    {
        type            turbulentIntensityKineticEnergyInlet;
        intensity       0.0628;
        value           uniform 1;
    }
    inletAir
    {
        type            turbulentIntensityKineticEnergyInlet;
        intensity       0.0471;
        value           uniform 1;
    }
    outlet
    {
        type            inletOutlet;
        inletValue      nonuniform List<scalar> 
0
;
        value           nonuniform List<scalar> 
0
;
    }
    axis
    {
        type            empty;
    }
    frontAndBack_pos
    {
        type            wedge;
    }
    frontAndBack_neg
    {
        type            wedge;
    }
    procBoundary2to0
    {
        type            processor;
        value           uniform 30;
    }
    procBoundary2to3
    {
        type            processor;
        value           uniform 30;
    }
}


// ************************************************************************* //
