/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       volVectorField;
    location    "0";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   uniform (0 0 0.9);

boundaryField
{
    inletCH4
    {
        type            fixedValue;
        value           uniform (0 0 49.6);
    }
    wallOutside
    {
        type            zeroGradient;
    }
    wallTube
    {
        type            noSlip;
    }
    inletPilot
    {
        type            fixedValue;
        value           uniform (0 0 11.4);
    }
    inletAir
    {
        type            fixedValue;
        value           uniform (0 0 0.9);
    }
    outlet
    {
        type            pressureInletOutletVelocity;
        value           nonuniform List<vector> 
0
;
    }
    axis
    {
        type            empty;
    }
    frontAndBack_pos
    {
        type            wedge;
    }
    frontAndBack_neg
    {
        type            wedge;
    }
    procBoundary2to0
    {
        type            processor;
        value           uniform (0 0 0.9);
    }
    procBoundary2to3
    {
        type            processor;
        value           uniform (0 0 0.9);
    }
}


// ************************************************************************* //
