/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      ascii;
    class       dictionary;
    object      blockMeshDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

convertToMeters 0.001;

vertices
(
   (0     0                    -100)   // 0

   (3.6  -0.15717942211764708  -100)   // 1
   (3.6   0.15717942211764708  -100)   // 2

   (3.85 -0.168094659764705905 -100)   // 3
   (3.85  0.168094659764705905 -100)   // 4

   (9.1  -0.39731465035294123  -100)   // 5
   (9.1   0.39731465035294123  -100)   // 6

   (0     0                     0)     // 7

   (3.6  -0.15717942211764708   0)     // 8
   (3.6   0.15717942211764708   0)     // 9

   (3.85 -0.168094659764705905  0)     // 10
   (3.85  0.168094659764705905  0)     // 11

   (9.1  -0.39731465035294123   0)     // 12
   (9.1   0.39731465035294123   0)     // 13


   (150  -6.549142588235295     0)     // 14
   (150   6.549142588235295     0)     // 15

   (0     0                     500)   // 16

   (3.6  -0.15717942211764708   500)   // 17
   (3.6   0.15717942211764708   500)   // 18

   (3.85 -0.168094659764705905  500)   // 19
   (3.85  0.168094659764705905  500)   // 20

   (9.1  -0.39731465035294123   500)   // 21
   (9.1   0.39731465035294123   500)   // 22

   (150  -6.549142588235295     500)   // 23
   (150   6.549142588235295     500)   // 24
);

blocks
(
    hex ( 0  1  2  0  7  8  9  7)   (5  1 20)   simpleGrading (1 1 1)
    hex ( 3  5  6  4 10 12 13 11)   (5  1 20)   simpleGrading (1 1 1)

    hex ( 7  8  9  7 16 17 18 16)   (5  1 70)   simpleGrading (1 1 2)
    hex ( 8 10 11  9 17 19 20 18)   (1  1 70)   simpleGrading (1 1 2)
    hex (10 12 13 11 19 21 22 20)   (5  1 70)   simpleGrading (1 1 2)
    hex (12 14 15 13 21 23 24 22)   (60 1 70)   simpleGrading (3 1 2)
);

boundary
(
    inletCH4
    {
        type patch;
        faces
        (
            (1 0 0 2)
        );
    }

    wallOutside
    {
        type wall;
        faces
        (
            (14 15 24 23)
        );
    }

    wallTube
    {
        type wall;
        faces
        (
            (1 2 9 8)
            (10 11 9 8)
            (4 3 10 11)
            (5 6 13 12)
        );
    }

    inletPilot
    {
        type patch;
        faces
        (
            (5 3 4 6)
        );
    }

    inletAir
    {
        type patch;
        faces
        (
            (14 12 13 15)
        );
    }

    outlet
    {
        type patch;
        faces
        (
            (16 17 18 16)
            (17 19 20 18)
            (19 21 22 20)
            (21 22 24 23)
        );
    }

    axis
    {
        type empty;
        faces
        (
            (0 7 7 0)
            (7 16 16 7)
        );
    }

    frontAndBack_pos
    {
        type wedge;
        faces
        (
            (2 0 7 9)
            (6 4 11 13)

            (9 7 16 18)
            (11 9 18 20)
            (13 11 20 22)
            (15 13 22 24)
        );
    }

    frontAndBack_neg
    {
        type wedge;
        faces
        (
            (0 1 8 7)
            (3 5 12 10)

            (7 8 17 16)
            (8 10 19 17)
            (10 12 21 19)
            (12 14 23 21)
        );
    }
);

// ************************************************************************* //
