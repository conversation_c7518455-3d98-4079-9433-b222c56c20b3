/*---------------------------------------------------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
Build  : 13-65eda17ad4ca
Exec   : setFields
Date   : Aug 19 2025
Time   : 19:10:54
Host   : "station1"
PID    : 3738350
I/O    : uncollated
Case   : /home/<USER>/Documents/SandiaD_LTS_norea
nProcs : 1
sigFpe : Enabling floating point exception trapping (FOAM_SIGFPE).
fileModificationChecking : Monitoring run-time modified files using timeStampMaster (fileModificationSkew 10)
allowSystemOperations : Allowing user-supplied system call operations

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
Create time

Create mesh for time = 0

Reading "setFieldsDict"

Setting field default values
    Setting volScalarField::Internal T
    Setting volScalarField::Internal N2
    Setting volScalarField::Internal O2
    Setting volScalarField::Internal CH4

Setting field zone values
Zone: fuel
    Setting volScalarField::Internal CH4
    Setting volScalarField::Internal O2
    Setting volScalarField::Internal N2

End

