/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       volScalarField;
    location    "0";
    object      H2;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [];

internalField   uniform 0;

boundaryField
{
    inletCH4
    {
        type            fixedValue;
        value           nonuniform List<scalar> 
0
;
    }
    wallOutside
    {
        type            zeroGradient;
    }
    wallTube
    {
        type            zeroGradient;
    }
    inletPilot
    {
        type            fixedValue;
        value           nonuniform List<scalar> 
0
;
    }
    inletAir
    {
        type            fixedValue;
        value           nonuniform List<scalar> 
0
;
    }
    outlet
    {
        type            inletOutlet;
        inletValue      uniform 0;
        value           uniform 0;
    }
    axis
    {
        type            empty;
    }
    frontAndBack_pos
    {
        type            wedge;
    }
    frontAndBack_neg
    {
        type            wedge;
    }
    procBoundary1to0
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary1to3
    {
        type            processor;
        value           uniform 0;
    }
}


// ************************************************************************* //
