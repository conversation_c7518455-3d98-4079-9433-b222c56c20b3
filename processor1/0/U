/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       volVectorField;
    location    "0";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   uniform (0 0 0.9);

boundaryField
{
    inletCH4
    {
        type            fixedValue;
        value           nonuniform List<vector> 
0
;
    }
    wallOutside
    {
        type            zeroGradient;
    }
    wallTube
    {
        type            noSlip;
    }
    inletPilot
    {
        type            fixedValue;
        value           nonuniform List<vector> 
0
;
    }
    inletAir
    {
        type            fixedValue;
        value           nonuniform List<vector> 
0
;
    }
    outlet
    {
        type            pressureInletOutletVelocity;
        value           uniform (0 0 0.9);
    }
    axis
    {
        type            empty;
    }
    frontAndBack_pos
    {
        type            wedge;
    }
    frontAndBack_neg
    {
        type            wedge;
    }
    procBoundary1to0
    {
        type            processor;
        value           uniform (0 0 0.9);
    }
    procBoundary1to3
    {
        type            processor;
        value           uniform (0 0 0.9);
    }
}


// ************************************************************************* //
