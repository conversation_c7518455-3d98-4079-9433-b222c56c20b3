/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       volScalarField;
    location    "0";
    object      p;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [1 -1 -2 0 0 0 0];

internalField   uniform 100000;

boundaryField
{
    inletCH4
    {
        type            zeroGradient;
    }
    wallOutside
    {
        type            zeroGradient;
    }
    wallTube
    {
        type            zeroGradient;
    }
    inletPilot
    {
        type            zeroGradient;
    }
    inletAir
    {
        type            zeroGradient;
    }
    outlet
    {
        type            entrainmentPressure;
        rho             rho;
        psi             none;
        gamma           1;
        p0              uniform 100000;
        value           uniform 100000;
    }
    axis
    {
        type            empty;
    }
    frontAndBack_pos
    {
        type            wedge;
    }
    frontAndBack_neg
    {
        type            wedge;
    }
    procBoundary1to0
    {
        type            processor;
        value           uniform 100000;
    }
    procBoundary1to3
    {
        type            processor;
        value           uniform 100000;
    }
}


// ************************************************************************* //
