/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       volScalarField;
    location    "0";
    object      G;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [1 0 -3 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    inletCH4
    {
        type            MarshakRadiation;
        T               T;
        emissivityMode  lookup;
        emissivity      uniform 1;
        value           nonuniform List<scalar> 
0
;
    }
    wallOutside
    {
        type            MarshakRadiation;
        T               T;
        emissivityMode  lookup;
        emissivity      uniform 1;
        value           uniform 0;
    }
    wallTube
    {
        type            MarshakRadiation;
        T               T;
        emissivityMode  lookup;
        emissivity      uniform 1;
        value           nonuniform List<scalar> 
0
;
    }
    inletPilot
    {
        type            MarshakRadiation;
        T               T;
        emissivityMode  lookup;
        emissivity      uniform 1;
        value           nonuniform List<scalar> 
0
;
    }
    inletAir
    {
        type            MarshakRadiation;
        T               T;
        emissivityMode  lookup;
        emissivity      uniform 1;
        value           nonuniform List<scalar> 
0
;
    }
    outlet
    {
        type            MarshakRadiation;
        T               T;
        emissivityMode  lookup;
        emissivity      uniform 1;
        value           uniform 0;
    }
    axis
    {
        type            empty;
    }
    frontAndBack_pos
    {
        type            wedge;
    }
    frontAndBack_neg
    {
        type            wedge;
    }
    procBoundary1to0
    {
        type            processor;
        value           uniform 0;
    }
    procBoundary1to3
    {
        type            processor;
        value           uniform 0;
    }
}


// ************************************************************************* //
