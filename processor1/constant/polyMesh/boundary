/*--------------------------------*- C++ -*----------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Version:  13
     \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    format      binary;
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

11
(
    inletCH4
    {
        type            patch;
        nFaces          0;
        startFace       2527;
    }
    wallOutside
    {
        type            wall;
        inGroups        List<word> 1(wall);
        nFaces          37;
        startFace       2527;
    }
    wallTube
    {
        type            wall;
        inGroups        List<word> 1(wall);
        nFaces          0;
        startFace       2564;
    }
    inletPilot
    {
        type            patch;
        nFaces          0;
        startFace       2564;
    }
    inletAir
    {
        type            patch;
        nFaces          0;
        startFace       2564;
    }
    outlet
    {
        type            patch;
        nFaces          38;
        startFace       2564;
    }
    axis
    {
        type            empty;
        inGroups        List<word> 1(empty);
        nFaces          0;
        startFace       2602;
    }
    frontAndBack_pos
    {
        type            wedge;
        inGroups        List<word> 1(wedge);
        nFaces          1301;
        startFace       2602;
    }
    frontAndBack_neg
    {
        type            wedge;
        inGroups        List<word> 1(wedge);
        nFaces          1301;
        startFace       3903;
    }
    procBoundary1to0
    {
        type            processor;
        inGroups        List<word> 1(processor);
        nFaces          40;
        startFace       5204;
        matchTolerance  0.0001;
        myProcNo        1;
        neighbProcNo    0;
    }
    procBoundary1to3
    {
        type            processor;
        inGroups        List<word> 1(processor);
        nFaces          35;
        startFace       5244;
        matchTolerance  0.0001;
        myProcNo        1;
        neighbProcNo    3;
    }
)

// ************************************************************************* //
